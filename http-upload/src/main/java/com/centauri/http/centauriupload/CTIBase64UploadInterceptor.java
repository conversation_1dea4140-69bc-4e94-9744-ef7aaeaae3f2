package com.centauri.http.centauriupload;

import android.util.Base64;
import android.util.Base64OutputStream;

import com.centauri.http.core.HttpLog;
import com.centauri.http.core.Interceptor;
import com.centauri.http.core.Request;
import com.centauri.http.core.Response;

import java.io.File;
import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.io.IOException;

/**
 * Created by cheneyang on 2017/11/3.
 */

public class CTIBase64UploadInterceptor implements Interceptor {
    @Override
    public Response intercept(Request request, Response previousResp) {
        if (request instanceof CTIUploadRequest) {
            long timeStart = System.currentTimeMillis();

            File gzipFile = ((CTIUploadRequest) request).getFile();
            String path = gzipFile.getParent() + File.separator + gzipFile.getName() + ".encode";

            File encodeFile = new File(path);
            if (encodeFile.exists()) {
                encodeFile.delete();
            }

            FileInputStream fin = null;
            FileOutputStream fout = null;
            Base64OutputStream base64OutputStream = null;
            try {
                fout = new FileOutputStream(path);
                base64OutputStream = new Base64OutputStream(fout, Base64.NO_WRAP);
                fin = new FileInputStream(gzipFile);
                byte[] buffer = new byte[3 * 1024];
                int len;
                while ((len = fin.read(buffer)) != -1) {
                    base64OutputStream.write(buffer, 0, len);
                }
                base64OutputStream.flush();
                fout.flush();

                ((CTIUploadRequest) request).setFile(new File(path));
                HttpLog.d("HTTP-UPLOAD-BASE64", "Base64 Encode completed: " + path);
            } catch (Exception e) {
                e.printStackTrace();
                HttpLog.e("HTTP-UPLOAD-BASE64", "Base64 Encode failed: " + e.getMessage());
                //编码失败就要停止后续的流程
                previousResp.needBreakOtherInterceptors = true;
            } finally {
                if (base64OutputStream != null) {
                    try {
                        base64OutputStream.close();
                    } catch (IOException e) {
                        e.printStackTrace();
                    }
                }
                if (fin != null) {
                    try {
                        fin.close();
                    } catch (IOException e) {
                        e.printStackTrace();
                    }
                }
                if (fout != null) {
                    try {
                        fout.close();
                    } catch (IOException e) {
                        e.printStackTrace();
                    }
                }
            }
            HttpLog.d("HTTP-UPLOAD-TIME", "Base64 time: " + (System.currentTimeMillis() - timeStart));
        }
        return previousResp;
    }
}
