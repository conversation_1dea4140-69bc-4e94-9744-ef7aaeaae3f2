package com.centauri.http.centauriupload;

import android.text.TextUtils;

import com.centauri.http.core.Call;
import com.centauri.http.core.Delivery;
import com.centauri.http.core.Dispatcher;
import com.centauri.http.core.ExecutableCall;
import com.centauri.http.core.HttpHandler;
import com.centauri.http.core.INetworkManager;
import com.centauri.http.core.Interceptor;
import com.centauri.http.core.MainThreadDelivery;
import com.centauri.http.core.Request;

import java.util.ArrayList;
import java.util.concurrent.ConcurrentLinkedQueue;

/**
 * Created by cheneyang on 2017/11/3.
 */

public class CTIUploadNetworkManager implements INetworkManager {

    private final ArrayList<Interceptor> frontInterceptors = new ArrayList<>();
    private final ArrayList<Interceptor> builtinInterceptors = new ArrayList<>();
    private final ArrayList<Interceptor> endInterceptors = new ArrayList<>();

    // 默认的每个请求的最多重试次数，如果请求本身没有设置最大重试次数，则会用这个数值
    private int defaultMaxRetryTimes = 2;

    // 默认的Http连接超时设置，如果Request里没有设置，则会用这里的超时时间
    // 如果Request里也设置了超时时间，则会优先使用Request里的
    public int defaultConnectTimeout = 15000;
    public int defaultReadTimeout = 15000;

    private CTIHttpUploadInterceptor httpUploadInterceptor;

    private Dispatcher dispatcher;
    // 结果分发器，比如分发到主线程
    private Delivery delivery;

    // 考虑到线程安全，不能用ArrayList
    private final ConcurrentLinkedQueue<ExecutableCall> allAsyncUploadCall = new ConcurrentLinkedQueue<>();

    public CTIUploadNetworkManager() {
        httpUploadInterceptor = new CTIHttpUploadInterceptor(this);

        builtinInterceptors.add(new CTIGzipUploadInterceptor());
        builtinInterceptors.add(new CTIBase64UploadInterceptor());
        builtinInterceptors.add(httpUploadInterceptor);

        dispatcher = new Dispatcher();
        delivery = new MainThreadDelivery();
    }

    @Override
    public void addHttpHandler(HttpHandler httpHandler) {
        if (httpHandler != null) {
            httpUploadInterceptor.addHttpHandler(httpHandler);
        }
    }

    @Override
    public void registerAsyncHttpCall(final ExecutableCall call) {
        if (call != null) {
            allAsyncUploadCall.add(call);
        }
    }

    @Override
    public void unregisterAsyncHttpCall(ExecutableCall call) {
        if (call != null) {
            allAsyncUploadCall.remove(call);
        }
    }

    @Override
    public void cancelRequestByName(String name) {
        if (TextUtils.isEmpty(name)) {
            return;
        }

        // 遍历所有async http call，如果名字和给定的参数相同，则取消它
        for (ExecutableCall call : allAsyncUploadCall) {
            if (call != null && name.equals(call.getRequestName())) {
                call.cancel();
            }
        }
    }

    @Override
    public void cancelAllRequest() {
        // 遍历所有async http call，如果名字和给定的参数相同，则取消它
        for (ExecutableCall call : allAsyncUploadCall) {
            call.cancel();
        }
    }

    @Override
    public ArrayList<Interceptor> getAllInterceptors() {
        final ArrayList<Interceptor> all = new ArrayList<>();
        all.addAll(frontInterceptors);
        all.addAll(builtinInterceptors);
        all.addAll(endInterceptors);

        return all;
    }

    @Override
    public ArrayList<Interceptor> getBuiltinInterceptors() {
        return builtinInterceptors;
    }

    @Override
    public void addFistInterceptor(Interceptor it) {
        if (it != null) {
            frontInterceptors.add(it);
        }
    }

    @Override
    public void setDefaultMaxRetryTimes(final int maxRetryTimes) {
        this.defaultMaxRetryTimes = maxRetryTimes;
    }

    @Override
    public int getDefaultMaxRetryTimes() {
        return defaultMaxRetryTimes;
    }

    @Override
    public void addLastInterceptor(Interceptor it) {
        if (it != null) {
            endInterceptors.add(it);
        }
    }

    @Override
    public Dispatcher dispatcher() {
        return dispatcher;
    }

    @Override
    public Delivery delivery() {
        return delivery;
    }

    @Override
    public void setDelivery(Delivery delivery) {
        if (delivery != null) {
            this.delivery = delivery;
        }
    }

    @Override
    public Call newCall(Request request) {
        return new CTIHttpUploadCall(request, this);
    }
}
