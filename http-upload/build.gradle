apply plugin: 'com.android.library'
apply plugin: 'maven-publish'

android {
    compileSdkVersion 26
    buildToolsVersion "25.0.2"

    defaultConfig {
        minSdkVersion 22
        targetSdkVersion 26

        versionCode 20000
        versionName "2.00.00"
    }
    buildTypes {
        release {
            minifyEnabled false
            proguardFiles getDefaultProguardFile('proguard-android.txt'), 'proguard-rules.pro'
        }
    }
}

configurations {
    myConfig
}

dependencies {
    compile fileTree(include: ['*.jar'], dir: 'libs')
    compile project(':http-core')
    //myConfig project(':http-core')
//    compile(name: 'http-core-release', ext: 'aar')
}

task sourcesJar(type: Jar, dependsOn: "assembleRelease") {
    from "build/intermediates/packaged-classes/release"

//    from {
//        configurations.compile.collect {
//            it.isDirectory() ? it : zipTree(it)
//        }
//    }

    exclude('**/test/*.class', '**/sample/*.class')
    exclude "**/R.class"
    exclude "**/R\$*.class"
}

group 'com.centauri.comm'
version "${android.defaultConfig.versionName}-SNAPSHOT"    //-SNAPSHOT

publishing {
    repositories {
//        maven {
//            //发布到本地
//            url project.uri("${project.buildDir.absolutePath}\\maven");
//        }

        maven {
            //发布到腾讯内部仓库-snapshots，非release仓库，同一个版本号可以upload多次
            //需要版本号加上后缀：'-SNAPSHOT' 才能生效,  比如 '1.3.2-SNAPSHOT'
            credentials {
                username 'g_midas'
                password 'aca5de342b7e11eab9396c92bf5e3645'
            }
            url project.uri("https://mirrors.tencent.com/repository/maven/thirdparty/")
        }
//
//        maven {
//            //发布到腾讯内部仓库，release仓库，同一个版本号只允许upload一次
//            credentials {
//                username 'rdm'
//                password 'rdm'
//            }
//            url project.uri("http://maven.oa.com/nexus/content/repositories/thirdparty")
//        }
    }

    publications {
        http(MavenPublication) {
            artifactId 'centauricommon-http-upload'
            artifact sourcesJar
            //The publication doesn't know about our dependencies, so we have to manually add them to the pom
            pom.withXml {
                def dependenciesNode = asNode().appendNode('dependencies')

                //Iterate over the compile dependencies (we don't want the test ones), adding a <dependency> node for each
                configurations.compile.allDependencies.each {
                    if (it instanceof ExternalModuleDependency) {
                        def dependencyNode = dependenciesNode.appendNode('dependency')
                        dependencyNode.appendNode('groupId', it.group)
                        dependencyNode.appendNode('artifactId', it.name)
                        dependencyNode.appendNode('version', it.version)
                    }
                }
            }
        }
    }
}

task copyLibs(type: Copy) {
    from configurations.myConfig
    into 'libs'
}