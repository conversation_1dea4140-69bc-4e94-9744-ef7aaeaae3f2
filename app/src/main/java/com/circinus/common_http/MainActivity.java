package com.circinus.common_http;

import android.app.Activity;
import android.os.Bundle;
import android.os.Environment;
import android.util.Log;
import android.view.View;

//import com.golshadi.majid.core.DownloadManagerPro;
//import com.golshadi.majid.report.listener.DownloadManagerListener;

import com.circinus.http.circinushttp.CTIEncodeKey;

import java.io.File;
import java.io.UnsupportedEncodingException;
import java.security.SecureRandom;

public class MainActivity extends Activity {

    private static final String TAG = "Test";
    public static String MIX_MD5;
    public static String strEncodeKey;

    static {

    }

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_main);

    }

    public void onClick(View view) {

        final String sdPath = Environment.getExternalStorageDirectory().getAbsolutePath();
        File file = new File(sdPath + "/Tencent/OpenMidas2/");

        String strPath = Environment.getExternalStorageDirectory().getAbsolutePath() + "/Tencent/OpenMidas/";

        File fFile = new File(strPath);
        if (!fFile.exists()) {
            fFile.mkdir();
        }

        if (!file.exists()) {
            boolean result = file.mkdirs();

            Log.e(TAG, "Mk dir = " + file.getAbsolutePath());
            Log.e(TAG, "Mk dir result = " + result);
        } else {
            Log.e(TAG, "Mk dir result exist ");
        }

/*

        DownloadManagerPro dm = new DownloadManagerPro(this);

        String path = "FaipTest";
        int chunk = 2;

        dm.init(path, chunk, new DownloadManagerListener() {
            @Override
            public void OnDownloadStarted(long taskId) {
                Log.d(TAG, "OnDownloadStarted " + taskId);
            }

            @Override
            public void OnDownloadPaused(long taskId) {
                Log.d(TAG, "OnDownloadPaused " + taskId);
            }

            @Override
            public void onDownloadProcess(long taskId, double percent, long downloadedLength) {
                Log.d(TAG, "onDownloadProcess " + taskId);
                Log.d(TAG, "onDownloadProcess percent " + percent);
                Log.d(TAG, "onDownloadProcess downloadedLength " + downloadedLength);
            }

            @Override
            public void OnDownloadFinished(long taskId) {
                Log.d(TAG, "OnDownloadFinished " + taskId);
            }

            @Override
            public void OnDownloadRebuildStart(long taskId) {
                Log.d(TAG, "OnDownloadRebuildStart " + taskId);
            }

            @Override
            public void OnDownloadRebuildFinished(long taskId) {
                Log.d(TAG, "OnDownloadRebuildFinished " + taskId);
            }

            @Override
            public void OnDownloadCompleted(long taskId) {
                Log.d(TAG, "OnDownloadCompleted " + taskId);
            }

            @Override
            public void connectionLost(long taskId) {
                Log.d(TAG, "connectionLost " + taskId);
            }
        });
*/

        String saveName = "Test.apk";
        String url = "http://imtt.dd.qq.com/16891/F3FB1B8E292D1CB24151D3FEF6F348D7.apk";
        boolean overwrite = true;
        boolean priority = true;

        /*int taskToken = dm.addTask(saveName, url, overwrite, priority);

        try {
            dm.startDownload(taskToken);
        } catch (IOException e) {
            e.printStackTrace();
        }*/
    }
}
