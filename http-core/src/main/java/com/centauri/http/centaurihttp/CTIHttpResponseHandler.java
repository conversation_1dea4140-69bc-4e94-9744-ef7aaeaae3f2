package com.centauri.http.centaurihttp;

import android.content.Context;

import com.centauri.http.core.Request;
import com.centauri.http.core.Response;

/**
 * Responsible for packaging the Response of the underlying network module into CentauriHttpResponse
 * In CentauriHttpResponse, some basic json fields are parsed and when errors occur
 * Pack some error messages
 *
 * Created by a1w0n on 16/11/28.
 */
public class CTIHttpResponseHandler extends CTIBaseHttpHandler {

    private CTINetworkManager newNetworkManager;

    public CTIHttpResponseHandler(final CTINetworkManager newNetworkManager) {
        this.newNetworkManager = newNetworkManager;
    }

    @Override
    public void onHttpEnd(Request request, Response response) {
        super.onHttpEnd(request, response);

        if (request == null) {
            return;
        }

        if (response == null) {
            return;
        }

        if (newNetworkManager == null) {
            return;
        }

        // If it is not Master Mi’s own network request, there is
        // no need to wrap Response into CentauriHttpResponse at all
        if (!(request instanceof CTIHttpRequest)) {
            return;
        }

        // There is no need to judge the context here, let the bottom layer judge it
        final Context context = newNetworkManager.getContext();

        // Wrap the Response and get the packaged result
        CTIHttpResponse centauriHttpResponse = new CTIHttpResponse(context, response);
        // Put the packaged result in the bottom Response
        response.setTag(centauriHttpResponse);
    }

}
