package com.centauri.http.centaurihttp;

import android.text.TextUtils;

import com.centauri.http.core.Callback;
import com.centauri.http.core.Response;

/**
 * Encapsulates the Callback interface of base http
 * Every network request must inherit this class to implement custom result processing
 * Conveniently distribute to different network request result processors
 * Created by a1w0n on 16/11/15.
 */
public class CTIHttpAns implements Callback {

    @SuppressWarnings("WeakerAccess")
    protected int resultCode = -1;

    @SuppressWarnings("WeakerAccess")
    protected String resultMsg = "";


    @SuppressWarnings("WeakerAccess")
    protected String msgErrorCode = "";

    @SuppressWarnings("WeakerAccess")
    protected String errorMsg = "";

    // The complete Json string returned by the server
    @SuppressWarnings("WeakerAccess")
    protected String resultData;

    @SuppressWarnings("WeakerAccess")
    public Exception exception;

    private ICTIHttpCallback centauriHttpCallback;

    // Request corresponding to this Ans
    private CTIHttpRequest centauriHttpRequest;

    @SuppressWarnings("WeakerAccess")
    public CTIHttpAns(final ICTIHttpCallback callback) {
        centauriHttpCallback = callback;
    }

    /**
     * Set the Request corresponding to this Ans
     */
    public void setCentauriHttpRequest(final CTIHttpRequest request) {
        centauriHttpRequest = request;
    }

    // Get the requested request
    public CTIHttpRequest getCentauriHttpRequest() {
        return centauriHttpRequest;
    }

    // Business return code, ret in json format
    @SuppressWarnings("unused")
    public int getResultCode() {
        return resultCode;
    }

    public String getMsgErrorCode() {
        return msgErrorCode;
    }


    //Business return information, msg in json format
    @SuppressWarnings("unused")
    public String getResultMessage() {
        return resultMsg;
    }

    //Error message, system level
    @SuppressWarnings("unused")
    public String getErrorMessage() {
        return errorMsg;
    }

    @SuppressWarnings("unused")
    public String getResultData() {
        return resultData;
    }

    @Override
    public void onResponse(final Response response) {
        if (response != null) {
            // Save the complete Json string returned by the server for all subclasses
            resultData = response.responseBody;

            exception = response.exception;

            final CTIHttpResponse centauriHttpResponse =
                    CTIHttpResponse.getCentauriHttpResponseFromResponse(response);

            if (centauriHttpResponse != null) {
                resultMsg = centauriHttpResponse.centauriBusinessResultMsg;
                resultCode = centauriHttpResponse.centauriBusinessResultCode;
                errorMsg = centauriHttpResponse.centauriBusinessResultMsg;
            }
        }

        // If the Http request is successful
        if (response != null && response.isStopped) {
            boolean continueProcess = handleStop(response);

            // Automatic callback of upper-level business callback
            if (centauriHttpCallback != null && continueProcess) {
                centauriHttpCallback.onStop(this);
            }
        } else if (response != null && response.isSuccess()) {
            boolean continueProcess = handleSuccess(response);

            // Automatic callback of upper-level business callback
            if (centauriHttpCallback != null && continueProcess) {
                centauriHttpCallback.onSuccess(this);
            }
        } else {
            // If the Http request fails
            boolean continueProcess = handleFailure(response);

            // Automatic callback of upper-level business callback
            if (centauriHttpCallback != null && continueProcess) {
                centauriHttpCallback.onFailure(this);
            }
        }
    }

    /**
     * Unified processing logic of request success in request Ans
     * The return value represents whether to continue the payment process,
     * if it is false, then the rest of the payment process will not be followed
     * The default is true
     */
    @SuppressWarnings("WeakerAccess")
    protected boolean handleSuccess(@SuppressWarnings("unused") Response response) {
        return true;
    }

    /**
     * Unified processing logic of request failure in request Ans
     * The return value represents whether to continue the payment process,
     * if it is false, then the rest of the payment process will not be followed
     * The default is true
     */
    @SuppressWarnings("WeakerAccess")
    protected boolean handleFailure(@SuppressWarnings("unused") Response response) {
        return true;
    }

    /**
     * Unified processing of request suspension logic in request Ans
     * The return value represents whether to continue the payment process,
     * if it is false, then the rest of the payment process will not be followed
     * The default is true
     */
    @SuppressWarnings("WeakerAccess")
    protected boolean handleStop(@SuppressWarnings("unused") Response response) {
        return true;
    }

    /**
     * Allow subclasses to get the key used to encrypt the parameters in the original Request to decrypt
     */
    @SuppressWarnings("unused")
    protected String getDecodeKey() {
        if (centauriHttpRequest == null) {
            return "";
        }

        final CTIEncodeKey encodeKey = centauriHttpRequest.getEncodeKey();
        if (encodeKey == null || TextUtils.isEmpty(encodeKey.key)) {
            return "";
        }

        return encodeKey.key;
    }

    @SuppressWarnings("unused")
    public static CTIHttpAns generateFakeCentauriHttpAns(final int ret, final String msg) {
        final CTIHttpAns ans = new CTIHttpAns(null);
        ans.resultCode = ret;
        ans.resultMsg = msg;
        ans.errorMsg = msg;
        return ans;
    }
}
