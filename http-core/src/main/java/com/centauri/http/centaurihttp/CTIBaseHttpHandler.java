package com.centauri.http.centaurihttp;

import com.centauri.http.core.HttpHandler;
import com.centauri.http.core.Request;
import com.centauri.http.core.Response;

/**
 * The base class of all Master Mi's own HttpHandler
 * The purpose of designing this class is to prevent some handlers that only care about
 * onHttpStart from overriding the onHttpStop method
 * Another reason is that the callback method of HttpHandler may be added, deleted, and modified.
 * A base class is designed to change the callback method.
 * Less impact on the outside
 * Created by a1w0n on 2017/2/8.
 */
public class CTIBaseHttpHandler implements HttpHandler {

    @Override
    public void onHttpStart(final Request request) {
    }

    @Override
    public void onHttpEnd(final Request request, final Response response) {
    }

    /**
     * This function will be called back when the last network request failed, and it will be retried all the time
     *
     * @param currentRetry is about to retry the first few times
     * @param maxRetry     maximum number of retries
     * @param request      The request that needs to be retried
     * @param response     The last failed response
     */
    @Override
    public void onHttpRetry(int currentRetry, int maxRetry, Request request, Response response) {
    }
}
