package com.centauri.http.centaurihttp;

import com.centauri.http.core.Request;
import com.centauri.http.core.Response;

/**
 * Responsible for notifying the outside according to the success of
 * the request after each network request, in order to perform network time-consuming reporting
 * Or other reporting requirements
 *
 * Created by a1w0n on 2017/2/8.
 */
public class CTIHttpTimeReportHandler extends CTIBaseHttpHandler {

    private final CTINetworkManager centauriNetworkManager;

    public CTIHttpTimeReportHandler(CTINetworkManager centauriNetworkManager) {
        this.centauriNetworkManager = centauriNetworkManager;
    }

    /**
     * After each request is completed, a report will be performed
     */
    @Override
    public void onHttpEnd(Request request, Response response) {
        super.onHttpEnd(request, response);

        if (centauriNetworkManager == null) {
            return;
        }

        // Non-Mi Master network request, but also to report

        // If this network request is successful
        if (response != null && response.isSuccess()) {
            // If the network request is successful, call back to facilitate external reporting
            centauriNetworkManager.notifyNetworkSuccess(request, response);
        } else {
            centauriNetworkManager.notifyNetworkFailure(request, response);
        }
    }
}
