package com.centauri.http.centaurikey;

import android.text.TextUtils;

import com.centauri.comm.CTILog;
import com.centauri.http.centaurihttp.CTIEncodeKey;
import com.centauri.http.utils.SPUtils;

import javax.crypto.Cipher;
import javax.crypto.spec.IvParameterSpec;
import javax.crypto.spec.SecretKeySpec;

public class CTIToolAES {

    public static String doEncode(String encryptString, String encryptKey) {

        return encryptAES(encryptString, encryptKey);
    }

    public static String doDecode(String decryptString, String decryptKey) {
        byte[] tmp = parseHexStr2Byte(decryptString);
        return decryptAES(tmp, decryptKey);
    }

    public static String encryptAES(String encryptString, String encryptKey, String iv) {
        byte[] encryptData = null;
        try {
            SecretKeySpec key = new SecretKeySpec(encryptKey.getBytes(), SPUtils.getString(SPUtils.ALGORITHM_MODEL));
            Cipher cipher = Cipher.getInstance(SPUtils.getString(SPUtils.ACP_MODEL));

            if (!TextUtils.isEmpty(iv) && iv.length() >= 16) {
                cipher.init(Cipher.ENCRYPT_MODE, key, new IvParameterSpec(iv.getBytes()));
                encryptData = cipher.doFinal(encryptString.getBytes());
            }

        } catch (Exception e) {
            CTILog.w("encryptAESTools", String.valueOf(e));
        }
        if (encryptData != null) {
            return parseByte2HexStr(encryptData);
        }
        return encryptString;
    }

    public static String encryptAES(String encryptString, String encryptKey) {
        byte[] encryptData = null;
        try {
            SecretKeySpec key = new SecretKeySpec(encryptKey.getBytes(), SPUtils.getString(SPUtils.ALGORITHM_MODEL));
            Cipher cipher = Cipher.getInstance(SPUtils.getString(SPUtils.ACP_MODEL));

            String mixMd5 = CTIEncodeKey.MIX_MD5;
            if (!TextUtils.isEmpty(mixMd5) && mixMd5.length() >= 16) {
                cipher.init(Cipher.ENCRYPT_MODE, key, new IvParameterSpec(mixMd5.substring(0, 16).getBytes()));
                encryptData = cipher.doFinal(encryptString.getBytes());
            }

        } catch (Exception e) {
            CTILog.w("encryptAESTools", String.valueOf(e));
        }
        if (encryptData != null) {
            return parseByte2HexStr(encryptData);
        }
        return encryptString;
    }

    public static String decryptAES(byte[] decryptByte, String descryptKey) {
        byte[] decryptData = null;
        try {
            SecretKeySpec key = new SecretKeySpec(descryptKey.getBytes(), SPUtils.getString(SPUtils.ALGORITHM_MODEL));
            Cipher cipher = Cipher.getInstance(SPUtils.getString(SPUtils.ACP_MODEL));
            cipher.init(Cipher.DECRYPT_MODE, key,
                    new IvParameterSpec(CTIEncodeKey.MIX_MD5.substring(0, 16).getBytes()));
            decryptData = cipher.doFinal(decryptByte);
        } catch (Exception e) {
            CTILog.w("decryptAESTools", String.valueOf(e));
        }

        if (decryptData != null) {
            return new String(decryptData);
        }
        return "";
    }

    public static byte[] parseHexStr2Byte(String hexStr) {
        if (hexStr.length() < 1)
            return null;

        byte[] result = new byte[hexStr.length() / 2];

        for (int i = 0; i < hexStr.length() / 2; i++) {
            int high = Integer.parseInt(hexStr.substring(i * 2, i * 2 + 1), 16);
            int low = Integer.parseInt(hexStr.substring(i * 2 + 1, i * 2 + 2), 16);
            result[i] = (byte) (high * 16 + low);
        }
        return result;
    }

    public static String parseByte2HexStr(byte buf[]) {
        StringBuffer sb = new StringBuffer();
        for (int i = 0; i < buf.length; i++) {
            String hex = Integer.toHexString(buf[i] & 0xFF);
            if (hex.length() == 1) {
                hex = '0' + hex;
            }
            sb.append(hex.toUpperCase());
        }

        return sb.toString();
    }

}
