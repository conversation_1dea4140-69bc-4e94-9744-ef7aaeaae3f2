package com.centauri.http.centaurikey;

import android.content.Context;
import android.content.SharedPreferences;
import android.nfc.Tag;
import android.text.TextUtils;

import com.centauri.http.centaurihttp.CTIEncodeKey;
import com.centauri.http.core.HttpLog;

import java.security.SecureRandom;
import java.util.HashMap;

//import com.centauri.centauri.comm.//HttpLog;

/**
 * All Mi Master SDK
 * "3 key" general manager
 * Created by a1w0n on 2017/3/13.
 */
public class CTIKeyManager {

    private final HashMap<String, String> secretKeyMap = new HashMap<>();
    private final HashMap<String, String> cryptKeyMap = new HashMap<>();
    private final HashMap<String, String> cryptKeyTimeMap = new HashMap<>();

//    private static String AES_KEY;

    /**
     * sec key is not stored in the disk in plaintext
     * It is encrypted by AES and placed on the disk
     * Here returns the key required for AES encryption
     *
     * @return AES encryption and decryption key
     */
    private String getEncodeKey() {
        while (CTIEncodeKey.strEncodeKey.length() < 16) {
            SecureRandom random = new SecureRandom();
            CTIEncodeKey.strEncodeKey = CTIEncodeKey.strEncodeKey + random.nextInt();
        }
        String strT1 = CTIEncodeKey.strEncodeKey.substring(0, 4);
        String strT2 = CTIEncodeKey.strEncodeKey.substring(4, 8);
        String strT3 = CTIEncodeKey.strEncodeKey.substring(8, 12);
        String strT4 = CTIEncodeKey.strEncodeKey.substring(12, 16);

        return strT3 + strT2 + strT1 + strT4;
    }

    /**
     * Read out the 3 keys stored in the disk and put them in ram
     *
     * @param context    needs to provide context
     * @param openID     corresponds to the open id
     * @param offerID    corresponding offer id
     * @param sdkVersion corresponds to the SDK version number
     */
    public void loadKeyFromDisk(final Context context,
                                final String openID,
                                final String offerID,
                                final String sdkVersion) {

        if (TextUtils.isEmpty(offerID)) {
            return;
        }

        if (TextUtils.isEmpty(openID)) {
            return;
        }

        if (context == null) {
            return;
        }

        if (TextUtils.isEmpty(sdkVersion)) {
            return;
        }

        final String secKeyDisk = readSecretKeyFromDisk(context, openID, offerID, sdkVersion);
        final String cryKeyDisk = readCryptKeyFromDisk(context, openID, offerID, sdkVersion);
        final String cryKeyTimeDisk = readCryptKeyTimeFromDisk(context, openID, offerID, sdkVersion);

        if (!TextUtils.isEmpty(secKeyDisk) && !TextUtils.isEmpty(cryKeyDisk) ||
                !TextUtils.isEmpty(cryKeyTimeDisk)) {

            setSecretKeyToRam(openID, offerID, secKeyDisk);
            setCryptKeyToRam(openID, offerID, cryKeyDisk);
            setCryptKeyTimeToRam(openID, offerID, cryKeyTimeDisk);
        }
    }

    /**
     * Determine whether the key needs to be changed according to the specific open id and offer id
     * If it is found that the key needs to be changed, the existing incomplete key will be cleared first
     *
     * @param context    needs to provide context
     * @param openID     corresponds to the open id
     * @param offerID    corresponding offer id
     * @param sdkVersion corresponds to the SDK version number
     * @return returns whether the key needs to be changed
     */
    public boolean needChangeKey(final Context context,
                                 final String openID,
                                 final String offerID,
                                 final String sdkVersion) {

        if (TextUtils.isEmpty(offerID)) {
            return false;
        }

        if (TextUtils.isEmpty(openID)) {
            return false;
        }

        if (context == null) {
            return false;
        }

        if (TextUtils.isEmpty(sdkVersion)) {
            return false;
        }

        final String secKey = getSecretKeyFromRam(openID, offerID);
        final String cryKey = getCryptoKeyFromRam(openID, offerID);
        final String cryKeyTime = getCryptKeyTimeFromRam(openID, offerID);

        if (TextUtils.isEmpty(secKey) || TextUtils.isEmpty(cryKey) || TextUtils.isEmpty(cryKeyTime)) {
            // If the value in the current memory is empty, read it from the file again for judgment
            // To prevent that the key has been changed back during initialization,
            // but the process is newly started when the payment is called,
            // the key is re-read from the file to ensure data consistency

            final String secKeyDisk = readSecretKeyFromDisk(context, openID, offerID, sdkVersion);
            final String cryKeyDisk = readCryptKeyFromDisk(context, openID, offerID, sdkVersion);
            final String cryKeyTimeDisk =
                    readCryptKeyTimeFromDisk(context, openID, offerID, sdkVersion);

            if (TextUtils.isEmpty(secKeyDisk) || TextUtils.isEmpty(cryKeyDisk)
                    || TextUtils.isEmpty(cryKeyTimeDisk)) {

                // If the keys on the memory and on the disk are incomplete, clear all the corresponding keys
                clearAllKey(context, openID, offerID, sdkVersion);

                return true;
            }


            // The code runs here to show that in ram, at least one of the 3 keys is empty
            // and 3 keys in the disk are not empty
            // We need to help read the 3 keys from the disk to the disk to prevent this from returning,
            // no need to change the key, but the upper layer cannot get the key from the ram
            setSecretKeyToRam(openID, offerID, secKeyDisk);
            setCryptKeyToRam(openID, offerID, cryKeyDisk);
            setCryptKeyTimeToRam(openID, offerID, cryKeyTimeDisk);
        }

        return false;

    }

    /**
     * Try to get the secret key from the memory, you need to provide openID + offerID
     *
     * @param openID  corresponds to openID
     * @param offerID corresponding offerID
     * @return secret key
     */
    @SuppressWarnings("WeakerAccess")
    public String getSecretKeyFromRam(final String openID, final String offerID) {
        if (TextUtils.isEmpty(openID)) {
            return "";
        }

        if (TextUtils.isEmpty(offerID)) {
            return "";
        }

        final String totalKey = openID + "_" + offerID;

        String result = secretKeyMap.get(totalKey);
        if (result == null) {
            result = "";
        }

        return result;
    }

    public void setSecretKeyToRam(
            final String openID,
            final String offerID,
            final String secretKey) {

        if (TextUtils.isEmpty(openID)) {
            return;
        }

        if (TextUtils.isEmpty(offerID)) {
            return;
        }

        if (TextUtils.isEmpty(secretKey)) {
            return;
        }

        // When the secretKey in the memory is stored, the key is stored in the HashMap in the form of openid + offerid
        final String totalKey = openID + "_" + offerID;
        secretKeyMap.put(totalKey, secretKey);
    }

    /**
     * Try to get cry key from memory
     *
     * @return cry key
     */
    public String getCryptoKeyFromRam(final String openID, final String offerID) {
        if (TextUtils.isEmpty(openID)) {
            return "";
        }

        if (TextUtils.isEmpty(offerID)) {
            return "";
        }

        final String totalKey = openID + "_" + offerID;

        String result = cryptKeyMap.get(totalKey);

        if (TextUtils.isEmpty(result)) {
            result = "";
        }

        return result;
    }

    public void setCryptKeyToRam(final String openID, final String offerID, final String cryptKey) {
        if (TextUtils.isEmpty(openID)) {
            return;
        }

        if (TextUtils.isEmpty(offerID)) {
            return;
        }

        if (TextUtils.isEmpty(cryptKey)) {
            return;
        }

        // When the cryptKey in the memory is stored, the key is stored in the HashMap in the form of openid + offerid
        final String totalKey = openID + "_" + offerID;

        cryptKeyMap.put(totalKey, cryptKey);
    }

    /**
     * getCryptKeyTimeFromRam
     *
     * @param openID
     * @param offerID
     * @return
     */
    public String getCryptKeyTimeFromRam(final String openID, final String offerID) {
        if (TextUtils.isEmpty(openID)) {
            return "";
        }

        if (TextUtils.isEmpty(offerID)) {
            return "";
        }

        final String totalKey = openID + "_" + offerID;

        String result = cryptKeyTimeMap.get(totalKey);

        if (TextUtils.isEmpty(result)) {
            result = "";
        }

        return result;
    }

    public void setCryptKeyTimeToRam(
            final String openID,
            final String offerID,
            final String cryptKeyTime) {

        if (TextUtils.isEmpty(openID)) {
            return;
        }

        if (TextUtils.isEmpty(offerID)) {
            return;
        }

        if (TextUtils.isEmpty(cryptKeyTime)) {
            return;
        }

        // When the cryptKeyTime in the memory is stored, the key is stored in the HashMap in
        // the form of openid + offerid
        final String totalKey = openID + "_" + offerID;

        cryptKeyTimeMap.put(totalKey, cryptKeyTime);
    }

    /**
     * A synchronization lock must be added, otherwise the B thread cannot get it when the A thread is saving
     */
    private static synchronized String getDataFromSharedPreference(
            final Context context,
            final String name,
            final String key) {

        if (context == null) {
            return "";
        }

        if (TextUtils.isEmpty(name)) {
            return "";
        }

        if (TextUtils.isEmpty(key)) {
            return "";
        }

        final SharedPreferences preference = context.getSharedPreferences(
                name, Context.MODE_MULTI_PROCESS);

        return preference.getString(key, "");
    }


    /**
     * A synchronization lock must be added, otherwise the B thread cannot get it when the A thread is saving
     */
    private static synchronized void clearDataFromSharedPreference(
            final Context context,
            final String name,
            final String key) {

        if (context == null) {
            return;
        }

        if (TextUtils.isEmpty(name)) {
            return;
        }

        if (TextUtils.isEmpty(key)) {
            return;
        }

        SharedPreferences.Editor editor =
                context.getSharedPreferences(name, Context.MODE_MULTI_PROCESS).edit();

        editor.remove(key);
        // It’s best to use commit here to prevent thread A from trying to get the key when saving it,
        // but not getting it
        editor.commit();
    }

    /**
     * A synchronization lock must be added, otherwise the B thread cannot get it when the A thread is saving
     */
    private static synchronized void saveDataToSharedPreference(
            final Context context,
            final String name,
            final String key,
            final String value) {

        if (context == null) {
            return;
        }

        if (TextUtils.isEmpty(name)) {
            return;
        }

        if (TextUtils.isEmpty(key)) {
            return;
        }

        if (TextUtils.isEmpty(value)) {
            return;
        }

        SharedPreferences.Editor editor =
                context.getSharedPreferences(name, Context.MODE_MULTI_PROCESS).edit();

        editor.putString(key, value);
        // It’s best to use commit here to prevent thread A from trying to get the key when saving it,
        // but not getting it
        editor.apply();
    }

    /**
     * Read the sec key from the disk
     */
    public String readSecretKeyFromDisk(
            final Context context,
            final String openID,
            final String offerID,
            final String sdkVersion) {

        if (TextUtils.isEmpty(openID)) {
            return "";
        }

        if (TextUtils.isEmpty(offerID)) {
            return "";
        }

        if (context == null) {
            return "";
        }

        if (TextUtils.isEmpty(sdkVersion)) {
            return "";
        }

        final String key = getEncodeKey();

        String strEncodeKey = "";
        String strKey = "";

        try {
            strEncodeKey = getDataFromSharedPreference(
                    context,
                    "CentauriUnipay",
                    sdkVersion + "_" + offerID + "_" + openID + "_SecretEncodeKey");
        } catch (Exception e) {
            return "";
        }

        if (TextUtils.isEmpty(strEncodeKey)) {
        } else {
            strKey = CTIToolAES.doDecode(strEncodeKey, key);
        }

        if (TextUtils.isEmpty(strKey)) {
            strKey = "";
        }

        return strKey;
    }

    /**
     * Save the sec key to disk
     */
    public void saveSecretKeyToDisk(
            final Context context,
            final String openID,
            final String offerID,
            final String strKey,
            final String sdkVersion) {

        if (TextUtils.isEmpty(strKey)) {
            return;
        }

        if (TextUtils.isEmpty(openID)) {
            return;
        }

        if (TextUtils.isEmpty(offerID)) {
            return;
        }

        if (TextUtils.isEmpty(sdkVersion)) {
            return;
        }

        if (context == null) {
            return;
        }

        final String key = getEncodeKey();
        final String strEncodeKey = CTIToolAES.doEncode(strKey, key);

        saveDataToSharedPreference(
                context,
                "CentauriUnipay",
                sdkVersion + "_" + offerID + "_" + openID + "_SecretEncodeKey",
                strEncodeKey);
    }


    /**
     * Read cry key from disk
     */
    public String readCryptKeyFromDisk(
            final Context context,
            final String openID,
            final String offerID,
            final String sdkVersion) {

        if (TextUtils.isEmpty(openID)) {
            return "";
        }

        if (context == null) {
            return "";
        }

        if (TextUtils.isEmpty(offerID)) {
            return "";
        }

        if (TextUtils.isEmpty(sdkVersion)) {
            return "";
        }

        // Use sec key to decrypt cry key
        final String key = readSecretKeyFromDisk(context, openID, offerID, sdkVersion);

        if (TextUtils.isEmpty(key)) {
            return "";
        }

        String strEncodeKey = "";
        String strKey = "";

        try {
            strEncodeKey = getDataFromSharedPreference(
                    context,
                    "CentauriUnipay",
                    sdkVersion + "_" + offerID + "_" + openID + "_CryptEncodeKey");
        } catch (Exception e) {
        }

        if (TextUtils.isEmpty(strEncodeKey)) {
        } else {
            strKey = CTIToolAES.doDecode(strEncodeKey, key);
        }

        if (TextUtils.isEmpty(strKey)) {
            strKey = "";
        }

        return strKey;
    }

    /**
     * Save the key to disk, each openid corresponds to a different key
     */
    public void saveCryptKeyToDisk(
            final Context context,
            final String openID,
            final String offerID,
            final String strKey,
            final String sdkVersion) {

        if (TextUtils.isEmpty(strKey)) {
            return;
        }

        if (TextUtils.isEmpty(openID)) {
            return;
        }

        if (TextUtils.isEmpty(offerID)) {
            return;
        }

        if (TextUtils.isEmpty(sdkVersion)) {
            return;
        }

        if (context == null) {
            return;
        }

        final String key = getSecretKeyFromRam(openID, offerID);
        if (TextUtils.isEmpty(key)) {
            return;
        }

        String strEncodeKey = CTIToolAES.doEncode(strKey, key);

        saveDataToSharedPreference(
                context,
                "CentauriUnipay",
                sdkVersion + "_" + offerID + "_" + openID + "_CryptEncodeKey",
                strEncodeKey);
    }

    public String readCryptKeyTimeFromDisk(
            final Context context,
            final String openID,
            final String offerID,
            final String sdkVersion) {

        if (TextUtils.isEmpty(openID)) {
            return "";
        }

        if (TextUtils.isEmpty(offerID)) {
            return "";
        }

        if (TextUtils.isEmpty(sdkVersion)) {
            return "";
        }

        if (context == null) {
            return "";
        }

        String strKeyTime = "";

        try {
            strKeyTime = getDataFromSharedPreference(
                    context,
                    "CentauriUnipay",
                    sdkVersion + "_" + offerID + "_" + openID + "_CryptEncodeKeyTime");
        } catch (Exception e) {
        }

        return strKeyTime;
    }

    public void saveCryptKeyTimeToDisk(
            final Context context,
            final String openID,
            final String offerID,
            final String strKeyTime,
            final String sdkVersion) {

        if (TextUtils.isEmpty(strKeyTime)) {
            return;
        }

        if (TextUtils.isEmpty(openID)) {
            return;
        }

        if (TextUtils.isEmpty(offerID)) {
            return;
        }

        if (TextUtils.isEmpty(sdkVersion)) {
            return;
        }

        if (context == null) {
            return;
        }

        saveDataToSharedPreference(
                context,
                "CentauriUnipay",
                sdkVersion + "_" + offerID + "_" + openID + "_CryptEncodeKeyTime",
                strKeyTime);
    }

    /**
     * When encrypting, use the payment key to encrypt, but report 1094/1099
     * It is best to clear the payment key and the matching keytime together to prevent the getkey
     * request from using the old one that has expired
     * key to encrypt, cause getkey itself to return 1094
     *
     * @param context    context
     * @param openID     clear the key corresponding to which openid
     * @param offerID    Clear the key corresponding to which offerID
     * @param sdkVersion Clear the key corresponding to which sdkVersion
     */
    public void clearCryptKeyAndKeyTime(final Context context,
                                        final String openID,
                                        final String offerID,
                                        final String sdkVersion) {

        if (TextUtils.isEmpty(openID)) {
            return;
        }

        if (TextUtils.isEmpty(offerID)) {
            return;
        }

        if (TextUtils.isEmpty(sdkVersion)) {
            return;
        }

        if (context == null) {
            return;
        }

        clearDataFromSharedPreference(
                context,
                "CentauriUnipay",
                sdkVersion + "_" + offerID + "_" + openID + "_CryptEncodeKey");

        clearDataFromSharedPreference(
                context,
                "CentauriUnipay",
                sdkVersion + "_" + offerID + "_" + openID + "_CryptEncodeKeyTime");

        // Clear the key value in the memory
        final String totalKey = openID + "_" + offerID;
        cryptKeyMap.remove(totalKey);
        cryptKeyTimeMap.remove(totalKey);
    }

    /**
     * Clear all keys according to openid offerid sdkversion, including those on memory and disk
     */
    public void clearAllKey(final Context context,
                            final String openID,
                            final String offerID,
                            final String sdkVersion) {

        if (TextUtils.isEmpty(openID)) {
            return;
        }

        if (TextUtils.isEmpty(offerID)) {
            return;
        }

        if (TextUtils.isEmpty(sdkVersion)) {
            return;
        }

        if (context == null) {
            return;
        }

        clearDataFromSharedPreference(
                context,
                "CentauriUnipay",
                sdkVersion + "_" + offerID + "_" + openID + "_CryptEncodeKey");

        clearDataFromSharedPreference(
                context,
                "CentauriUnipay",
                sdkVersion + "_" + offerID + "_" + openID + "_SecretEncodeKey");

        clearDataFromSharedPreference(
                context,
                "CentauriUnipay",
                sdkVersion + "_" + offerID + "_" + openID + "_CryptEncodeKeyTime");

        // clear momery key
        final String totalKey = openID + "_" + offerID;
        cryptKeyMap.remove(totalKey);
        secretKeyMap.remove(totalKey);
        cryptKeyTimeMap.remove(totalKey);
    }
}
