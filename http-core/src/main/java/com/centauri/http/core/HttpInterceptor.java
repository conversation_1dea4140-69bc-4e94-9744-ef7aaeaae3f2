package com.centauri.http.core;

import android.text.TextUtils;

import com.centauri.comm.CTILog;

import org.apache.http.conn.ConnectTimeoutException;

import java.io.ByteArrayOutputStream;
import java.io.DataOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.net.HttpURLConnection;
import java.net.SocketTimeoutException;
import java.net.URL;
import java.net.URLConnection;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.ListIterator;
import java.util.Map;

import javax.net.ssl.HostnameVerifier;
import javax.net.ssl.HttpsURLConnection;
import javax.net.ssl.SSLSocketFactory;

/**
 * Interceptor processing network request, get Response from Http network
 * Created by a1w0n on 9/8/16.
 */
class HttpInterceptor implements Interceptor {

    private static final String TAG = "HTTP";

    // The maximum number of retries that can be configured externally
    // The number of retries for external configuration cannot be higher than this
    private static final int MAX_VALID_RETRY_TIME = 5;

    // The default connection timeout: 15s
    private static final int DEFAULT_CONNECT_TIMEOUT = 15000;
    // The default socket read timeout time: 15s
    private static final int DEFAULT_READ_TIMEOUT = 15000;

    private final ArrayList<HttpHandler> httpHandlers = new ArrayList<>();

    private final NetworkManager networkManager;

    HttpInterceptor(NetworkManager networkManager) {
        this.networkManager = networkManager;
    }

    /**
     * Get the result of the request from the network
     * Here we ignore the previousResp parameter,
     * because we have to get it ourselves and don’t care what the previous response is
     */
    @Override
    public Response intercept(Request request, Response previousResp) {
        // If the request is empty, it cannot be processed, so the previous result will be returned directly
        if (request == null) {
            return previousResp;
        }

        return getResponseFromHttpWithRetry(request);
    }

    /**
     * Responsible for sending the Request to the Http server and getting the corresponding Response
     *
     * @param request The Request that needs to be sent to the server
     * @return The response returned by the server, if everything is normal,
     *     if there is an exception, an empty Response with nothing inside will be returned
     */
    private Response getResponseFromHttpWithRetry(final Request request) {
        // Generate a default result
        Response resp = new Response();

        // If the request is empty, return a default Response
        if (request == null) {
            return resp;
        }

        // The corresponding Request is saved in the Response
        resp.setRequest(request);

        // Record the time when the request started
        // Even if there is a failure to retry later, this time will not be updated
        request.startTime = System.currentTimeMillis();

        // Get the maximum number of retries, if not, the number of retries is 0, that is, no retry will be performed
        int mrt = 0;
        if (networkManager != null) {
            // Get the default number of retries configured in networkManager
            final int mrtInManager = networkManager.getDefaultMaxRetryTimes();

            if (mrtInManager > MAX_VALID_RETRY_TIME) {
                mrt = MAX_VALID_RETRY_TIME;
            } else if (mrtInManager >= 0) {
                // The number of retries must be >= 0 to work
                mrt = mrtInManager;
            }
        }

        final int mrtInRequest = request.maxRetryTime;
        if (mrtInRequest > MAX_VALID_RETRY_TIME) {
            mrt = MAX_VALID_RETRY_TIME;
        } else if (mrtInRequest >= 0) {
            // The number of retries must be a value of >= 0 to work, if it is another value, it will be ignored
            mrt = mrtInRequest;
        }

        // Perform a network request and get the result
        resp = getResponseFromHttp(request, mrt <= 0);
        // If the network request is successful at one time, there is no need to worry about the logic of retrying
        // Return the successful result directly
        if (resp != null && resp.isSuccess()) {
            return resp;
        }

        // Went here to show that the first network request failed, and start to process the logic related to retry

        // The first request was unsuccessful, execute at most mrt retry
        for (int i = 0; i < mrt; i++) {
            // Before starting the retry, update the request's retryTimes counter
            request.retryTimes = i + 1;

            // Notify all handlers that http retry has occurred
            // Note that this is to notify the outside before retrying, not after
            callAllHandlerOnRetry(request.retryTimes, mrt, request, resp);

            // Perform a request retry and analyze whether the request is successful
            resp = getResponseFromHttp(request, i == (mrt - 1));
            if (resp != null && resp.isSuccess()) {
                // If this network retry is successful
                // Return the result of this successful retry
                return resp;
            }
        }

        // Return the Response corresponding to the last failed retry
        return resp;
    }

    /**
     * Set the Http request headers of this request
     *
     * @param request request instance, used to obtain the corresponding Headers
     */
    private void setHeaders(final HttpURLConnection connection, final Request request) {
        // If the request is empty, nothing can be done, just return
        if (request == null) {
            return;
        }

        final HashMap<String, String> headers = request.getHttpHeaders();
        // If headers are empty, nothing can be done, just return
        if (headers == null) {
            return;
        }

        // If the headers are empty, it means that the upper layer has not set any request headers,
        // and you can return directly
        if (headers.size() <= 0) {
            return;
        }

        for (Map.Entry<String, String> entry : headers.entrySet()) {
            final String key = entry.getKey();
            final String value = entry.getValue();
            if (!TextUtils.isEmpty(key)) {
                connection.setRequestProperty(key, value);
            }
        }
    }

    /**
     * Notify all registered HttpHandler onStart events
     */
    private void callAllHandlerOnStart(final Request request) {
        if (httpHandlers.size() == 0) {
            return;
        }

        // Positive order traversal
        for (HttpHandler handler : httpHandlers) {
            handler.onHttpStart(request);
        }
    }

    /**
     * Notify all registered HttpHandler onStop events
     */
    private void callAllHandlerOnStop(final Request request, final Response response) {
        if (httpHandlers.size() == 0) {
            return;
        }

        final int size = httpHandlers.size();

        ListIterator iterator = httpHandlers.listIterator(size);
        // Reverse order traversal onStart uses positive order, onEnd uses reverse order,
        // so that onStart and onEnd between different Handlers will not cross
        while (iterator.hasPrevious()) {
            final HttpHandler handler = (HttpHandler) iterator.previous();
            handler.onHttpEnd(request, response);
        }
    }

    /**
     * Notify all registered HttpHandler onRetry events
     */
    private void callAllHandlerOnRetry(final int currentRetry,
                                       final int maxRetry,
                                       final Request request,
                                       final Response response) {
        if (httpHandlers.size() == 0) {
            return;
        }

        for (HttpHandler handler : httpHandlers) {
            handler.onHttpRetry(currentRetry, maxRetry, request, response);
        }
    }

    /**
     * If a custom hostnameVerifier and sslSocketFactory are set in the Request
     * Here we take these two out and set them to HttpsURLConnection
     *
     * @param httpURLConnection corresponds to HttpsURLConnection
     * @param request           corresponds to the Request
     */
    private static void trySetCustomHttpsVerify(
            final HttpURLConnection httpURLConnection,
            final Request request) {

        if (httpURLConnection == null) {
            return;
        }

        if (request == null) {
            return;
        }

        // If it is not an Https request, there is no need to set an Https certificate validator
        if (!request.isHttpsRequest()) {
            return;
        }

        if (!(httpURLConnection instanceof HttpsURLConnection)) {
            return;
        }

        final HttpsURLConnection httpsURLConnection = (HttpsURLConnection) httpURLConnection;

        final HostnameVerifier hostnameVerifier = request.getCustomHostnameVerifier();
        if (hostnameVerifier != null) {
            httpsURLConnection.setHostnameVerifier(hostnameVerifier);
        }

        final SSLSocketFactory sslSocketFactory = request.getCustomSSLSocketFactory();
        if (sslSocketFactory != null) {
            httpsURLConnection.setSSLSocketFactory(sslSocketFactory);
        }
    }

    /**
     * Start the network connection, try to get the request result
     *
     * @param request    The encapsulated object of the network request
     * @param isFinalTry is the last retry, for example, a total of three attempts to connect to the Internet,
     *                   the first two isFinalTry is false
     *                   The third isFinalTry is true
     * @return returns the corresponding request result package object Response
     */
    private Response getResponseFromHttp(final Request request, final boolean isFinalTry) {
        final Response resp = new Response();

        if (request == null) {
            return resp;
        }

        resp.setRequest(request);

        // Call back onStart of all HttpHandlers
        callAllHandlerOnStart(request);

        // Record the timestamp of the network connection attempt of this request.
        // If it is the first network connection attempt, currentTryTime and startTime are the same
        // But if you fail to retry afterwards, startTime will not be updated,
        // but currentTryTime will be updated every time it fails and retry
        request.currentTryTime = System.currentTimeMillis();

        // Conveniently shut down when an exception occurs, and advance the declaration of these two io streams
        InputStream inputStream = null;
        ByteArrayOutputStream outputStream = null;

        HttpURLConnection httpURLConnection = null;

        String fullURLString = "";
        String allParams = "";

        try {
            // Reset the two time-consuming statistics members to prevent the last time
            // statistics from being reported when an exception occurs
            // For example, the first time it fails, getOutputStreamTime is 10000,
            // and then it goes somewhere abnormal this time.
            // No chance to update getOutputStreamTime, at this time the report
            // will continue to report the last number of 10000
            // This is not right
            request.resetGetOutputStreamTimeAndGetInputStreamTime();

            fullURLString = request.getFullURLString();

            // If you get an empty URL, return directly, if you return here,
            // the finally statement will still be executed
            if (TextUtils.isEmpty(fullURLString)) {
                return resp;
            }

            final URL currentURL = new URL(fullURLString);

            // Start the request, openConnection will not trigger any TCP packet sending
            final URLConnection urlConnection = currentURL.openConnection();
            if (urlConnection == null) {
                return resp;
            }

            if (!(urlConnection instanceof HttpURLConnection)) {
                return resp;
            }

            httpURLConnection = (HttpURLConnection) urlConnection;
            request.httpURLConnection = httpURLConnection;

            // disable cache
            httpURLConnection.setUseCaches(false);
            // Prohibit automatic redirection of the underlying network library
            // Live network users may be redirected to the router login interface or the operator renewal interface
            httpURLConnection.setInstanceFollowRedirects(false);

            // Set the timeout of Http
            setHttpTimeout(httpURLConnection, request);

            // Try to set up a custom Https verification process, if it is Https connection
            trySetCustomHttpsVerify(httpURLConnection, request);

            // Set the request header
            setHeaders(httpURLConnection, request);

            // Regardless of Get or Post, you need to set do input
            httpURLConnection.setDoInput(true);

            allParams = request.constructAllParams();

            // Data sent during post
            if (request.isPostRequest()) {
                httpURLConnection.setRequestMethod("POST");
                httpURLConnection.setDoOutput(true);

                if (request.hasParameters()) {
                    // Build Post data
                    if (!TextUtils.isEmpty(allParams)) {
                        final byte[] paramsBytes = allParams.getBytes("UTF-8");
                        final int contentLength = paramsBytes.length;
                        httpURLConnection.setRequestProperty("Content-Length",
                                String.valueOf(contentLength));

                        // With this, the request body will be sent in a stream,
                        // otherwise the bottom layer will keep buffering what you want to send for you
                        // Will send it out for you when you finish writing
                        httpURLConnection.setFixedLengthStreamingMode(contentLength);

                        final long temp = System.currentTimeMillis();
                        // Here is the main time-consuming point, initiate a connection,
                        // and obtain OutputStream to send data to the server

                        if (request.isStopped()) {
                            return resp;
                        }

                        final DataOutputStream outStream = new DataOutputStream(
                                httpURLConnection.getOutputStream());
                        outStream.write(paramsBytes);
                        request.currentGetOutputStreamTime = System.currentTimeMillis() - temp;
                        outStream.flush();
                        // Need to catch it here, it can't affect the follow-up process
                        try {
                            outStream.close();
                        } catch (IOException ignored) {
                            ignored.printStackTrace();
                        }
                    }
                }
            } else if (request.isGetRequest()) {
                // If it is a Get request
                httpURLConnection.setRequestMethod("GET");
            }

            if (request.isStopped()) {
                return resp;
            }
            final int respCode = httpURLConnection.getResponseCode();

            resp.resultCode = respCode;

            final long temp = System.currentTimeMillis();

            if (respCode == HttpURLConnection.HTTP_OK) {
                // Get Http input stream
                inputStream = httpURLConnection.getInputStream();
            } else {
                // Get Http error stream
                inputStream = httpURLConnection.getErrorStream();
            }

            // This is also a major time-consuming point to get the data returned by the server
            if (request.isStopped()) {
                return resp;
            }

            request.currentGetInputStreamTime = System.currentTimeMillis() - temp;
            outputStream = new ByteArrayOutputStream();
            int len; // The length of the byte array read each time
            byte[] buf = new byte[1024];

            while ((len = inputStream.read(buf)) > 0) {
                outputStream.write(buf, 0, len);
            }

            final byte[] bytes = outputStream.toByteArray();

            // Use utf-8 encoding to parse the byte stream
            resp.responseBody = new String(bytes, "UTF-8");

        } catch (ConnectTimeoutException e) {
            // Server connection timeout
            e.printStackTrace();
            resp.exception = e;
        } catch (SocketTimeoutException e) {
            // The server responds to the connection timeout
            e.printStackTrace();
            resp.exception = e;
        } catch (IOException e) {
            e.printStackTrace();
            resp.exception = e;
        } catch (Exception e) {
            // Whether to try after other failures
            e.printStackTrace();
            resp.exception = e;
        } finally {
            closeStream(inputStream, outputStream);

            // Prevent an error before the connection is started,
            // so it is necessary to judge that httpURLConnection is not empty
            if (httpURLConnection != null) {
                httpURLConnection.disconnect();
            }
            request.httpURLConnection = null;

            // The callback of some Handlers here is very time-consuming, for example: CentauriIPMeasureHandler(100+ms)
            // So the calculation of network time consumption must be before callAllHandlerOnStop
            request.currentTryTimeConsume = System.currentTimeMillis() - request.currentTryTime;

            // Determine if all retries have failed
            if (isFinalTry && resp.resultCode != HttpURLConnection.HTTP_OK) {
                request.isAllRetriesFailed = true;
            }

            // Call back onStop of all HttpHandler
            // The callback of some Handlers here is very time-consuming, for example: CentauriIPMeasureHandler(100+ms)
            // So the calculation of network time consumption must be before callAllHandlerOnStop
            callAllHandlerOnStop(request, resp);

            CTILog.d(TAG, "Network response url = " + fullURLString);
            CTILog.d(TAG, "Network response param = " + allParams);
            CTILog.d(TAG, "Network response code = " + resp.resultCode);
            CTILog.d(TAG, "Network response message = " + resp.responseBody);
            CTILog.e(TAG, "Network response exception = " + resp.exception
                    + " Is stop = " + request.isStopped());
        }

        return resp;
    }

    /**
     * Set the timeout time of httpURLConnection. If there is a default,
     * use the default. If the request has a separate timeout time, it will be used first
     * Request is set separately
     */
    private void setHttpTimeout(final HttpURLConnection httpURLConnection, final Request request) {
        if (httpURLConnection == null) {
            return;
        }

        int connectTimeout = DEFAULT_CONNECT_TIMEOUT;
        int readTimeout = DEFAULT_READ_TIMEOUT;

        if (networkManager != null) {
            connectTimeout = networkManager.defaultConnectTimeout;
            readTimeout = networkManager.defaultReadTimeout;
        }

        if (request != null && request.connectTimeout > 0) {
            connectTimeout = request.connectTimeout;
        }

        if (request != null && request.readTimeout > 0) {
            readTimeout = request.readTimeout;
        }

        // Prevent the connection timeout from being too small
        if (connectTimeout <= 0) {
            connectTimeout = DEFAULT_CONNECT_TIMEOUT;
        }

        // Prevent socket read timeout from being too small
        if (readTimeout <= 0) {
            readTimeout = DEFAULT_READ_TIMEOUT;
        }

        httpURLConnection.setConnectTimeout(connectTimeout);
        httpURLConnection.setReadTimeout(readTimeout);
    }

    /**
     * Turn off input and output streams
     */
    private void closeStream(final InputStream inputStream, final OutputStream outputStream) {
        try {
            if (inputStream != null) {
                inputStream.close();
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        try {
            if (outputStream != null) {
                outputStream.flush();
                outputStream.close();
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    /**
     * Add an Http handler to get a callback when a specific event occurs
     */
    void addHttpHandler(final HttpHandler handler) {
        if (handler != null) {
            httpHandlers.add(handler);
        }
    }
}
