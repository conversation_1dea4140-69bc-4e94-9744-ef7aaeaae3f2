package com.centauri.http.core;

import java.util.ArrayList;

/**
 * An Http task package
 * Created by a1w0n on 9/8/16.
 */
class HttpCall implements Call {

    // Request corresponding to Http request
    private final Request mRequest;

    private final NetworkManager mNetMgr;

    HttpCall(final NetworkManager mgr, final Request request) {
        mRequest = request;
        mNetMgr = mgr;
    }

    /**
     * Used to cancel this task
     */
    @Override
    public void cancel() {
        if (mRequest != null) {
            // Cancel the task, after canceling, it will not call back to the outside
            mRequest.stopRequest();
        }
    }

    /**
     * Insert this HttpCall into the task executor and let it execute the task
     *
     * @param callback The callback corresponding to the task
     */
    @Override
    public void enqueue(final Callback callback) {
        if (mNetMgr == null) {
            return;
        }

        final Dispatcher dispatcher = mNetMgr.dispatcher();
        if (dispatcher == null) {
            return;
        }

        // Wrap the task into AsyncHttpCall first, and then let the thread pool execute
        // Because AsyncHttpCall inherits Runnable
        final AsyncHttpCall asyncHttpCall = new AsyncHttpCall(callback, false);

        // Distribute and execute this http call
        dispatcher.enqueue(asyncHttpCall);
    }

    @Override
    public void enqueueWithNoCustomInterceptor(Callback callback) {
        if (mNetMgr == null) {
            return;
        }

        final Dispatcher dispatcher = mNetMgr.dispatcher();
        if (dispatcher == null) {
            return;
        }

        // Wrap the task into AsyncHttpCall first, and then let the thread pool execute
        // Because AsyncHttpCall inherits Runnable
        final AsyncHttpCall asyncHttpCall = new AsyncHttpCall(callback, true);

        // Distribute and execute this http call
        dispatcher.enqueue(asyncHttpCall);
    }

    /**
     * The function corresponding to this method is not implemented here
     */
    @Override
    public boolean isExecuted() {
        return false;
    }

    /**
     * The function corresponding to this method is not implemented here
     */
    @Override
    public boolean isCanceled() {
        return false;
    }

    /**
     * The Http Call is executed synchronously, so there is no need for callback parameters here
     */
    @Override
    public Response execute() {
        return getResponseWithInterceptorChain(false);
    }

    /**
     * Execute this Http Call synchronously, but ignore all custom HttpInterceptor
     * But the registered Handler will still be executed
     */
    @Override
    public Response executeWithNoCustomInterceptor() {
        return getResponseWithInterceptorChain(true);
    }

    /**
     * Execute this Http Call synchronously, but ignore all custom HttpInterceptor
     * But the registered Handler will still be executed
     */
    @Override
    public Response executeWithAllCustomInterceptor() {
        return getResponseWithInterceptorChain(false);
    }

    /**
     * Encapsulate HttpCall so that the thread pool can execute HttpCall
     * Because the thread pool can only execute Runnable
     */
    class AsyncHttpCall implements ExecutableCall {

        private final Callback mCallback;

        private final boolean withNoCustomInterceptors;

        AsyncHttpCall(final Callback callback, final boolean withNoCustomInterceptors) {
            mCallback = callback;
            this.withNoCustomInterceptors = withNoCustomInterceptors;
        }

        /**
         * Cancel this asynchronous request
         */
        @Override
        public void cancel() {
            if (mRequest != null) {
                mRequest.stopRequest();
            }
        }

        /**
         * Get the name of the request corresponding to this Http call
         */
        @Override
        public String getRequestName() {
            String name = "";

            if (mRequest != null) {
                name = mRequest.getClass().getSimpleName();
            }

            return name;
        }

        @Override
        public void run() {
            // Get the original name of the thread
            final String originName = Thread.currentThread().getName();

            try {
                // Set the thread name to the name of the current request + thread id name
                Thread.currentThread().setName(
                        getRequestName() + "-" + Thread.currentThread().getId());

                if (mNetMgr != null) {
                    mNetMgr.registerAsyncHttpCall(AsyncHttpCall.this);
                }

                // Chained processing of this request
                // Do not ignore custom interceptors
                final Response resp = getResponseWithInterceptorChain(withNoCustomInterceptors);

                if (mRequest != null) {
                    // Set the stop flag
                    if (mRequest.isStopped()) {
                        resp.isStopped = true;
                    }

                    // Each request can customize the result dispatcher, if it is not customized,
                    // the default dispatcher will be used
                    if (mRequest.delivery != null) {
                        mRequest.delivery.deliverResult(resp, mCallback);
                    } else if (mNetMgr != null && mNetMgr.delivery() != null) {
                        mNetMgr.delivery().deliverResult(resp, mCallback);
                    }
                }
            } finally {
                // Set the thread back to its original name
                Thread.currentThread().setName(originName);

                if (mNetMgr != null) {
                    mNetMgr.unregisterAsyncHttpCall(AsyncHttpCall.this);
                }
            }
        }
    }

    /**
     * Responsibility chain processing Request
     * Through the needBreakOtherInterceptors flag of Response
     * We allow an Interceptor to interrupt the entire Interceptor chain
     *
     * @param withNoCustomInterceptor Whether to ignore all custom Interceptors
     * @return Response corresponding to Http request
     */
    private Response getResponseWithInterceptorChain(boolean withNoCustomInterceptor) {
        Response response = new Response();
        response.setRequest(mRequest);

        if (mNetMgr == null) {
            return response;
        }

        ArrayList<Interceptor> interceptors;
        if (withNoCustomInterceptor) {
            // If you ignore all custom interceptors, you only care about the built-in interceptors
            interceptors = mNetMgr.getBuiltinInterceptors();
        } else {
            interceptors = mNetMgr.getAllInterceptors();
        }

        if (interceptors == null) {
            return response;
        }

        for (Interceptor itcp : interceptors) {
            // Pass the old response to an interceptor and get the new response
            response = itcp.intercept(mRequest, response);

            if (response != null && response.needBreakOtherInterceptors) {
                // After using this flag bit, immediately reset it to the default value
                response.resetNeedBreakOtherInterceptors();
                break;
            }
        }

        return response;
    }
}
