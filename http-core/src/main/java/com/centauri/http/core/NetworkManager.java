package com.centauri.http.core;

import android.text.TextUtils;

import java.util.ArrayList;
import java.util.concurrent.ConcurrentLinkedQueue;

/**
 * Http library management class
 * <p>
 * Created by a1w0n on 9/8/16.
 */
public class NetworkManager implements INetworkManager {

    private final ArrayList<Interceptor> frontInterceptors = new ArrayList<>();
    private final ArrayList<Interceptor> builtinInterceptors = new ArrayList<>();
    private final ArrayList<Interceptor> endInterceptors = new ArrayList<>();

    private Dispatcher mDispatcher;
    // Result dispatcher, such as dispatching to the main thread
    private Delivery delivery;

    private HttpInterceptor httpInterceptor;

    // The default maximum number of retries for each request,
    // if the request itself does not set the maximum number of retries, this value will be used
    private int defaultMaxRetryTimes = 2;

    // The default Http connection timeout setting, if there is
    // no setting in the Request, the timeout time here will be used
    // If the timeout period is also set in the Request, the request in the
    public int defaultConnectTimeout = 15000;
    public int defaultReadTimeout = 15000;

    // Considering thread safety, ArrayList cannot be used
    private final ConcurrentLinkedQueue<ExecutableCall> allAsyncHttpCall =
            new ConcurrentLinkedQueue<>();

    public NetworkManager(IHttpLog logInterface) {
        HttpLog.httpLogInterface = logInterface;

        httpInterceptor = new HttpInterceptor(this);

        builtinInterceptors.add(httpInterceptor);
        mDispatcher = new Dispatcher();

        delivery = new MainThreadDelivery();

    }

    @Override
    public void addHttpHandler(final HttpHandler handler) {
        if (handler != null) {
            httpInterceptor.addHttpHandler(handler);
        }
    }


    /**
     * Register network requests that are ready to run asynchronously,
     * so that the outside has the opportunity to cancel them
     *
     * @param call an asynchronous network request
     */
    @Override
    public void registerAsyncHttpCall(final ExecutableCall call) {
        if (call != null) {
            allAsyncHttpCall.add(call);
        }
    }

    /**
     * After an asynchronous network request is executed normally, unregister it
     * Prevent the queue from getting longer and longer
     *
     * @param call an asynchronous network request
     */
    @Override
    public void unregisterAsyncHttpCall(final ExecutableCall call) {
        if (call != null) {
            allAsyncHttpCall.remove(call);
        }
    }

    /**
     * Cancel network requests based on the class name
     *
     * @param name class name, excluding package name
     */
    @Override
    public void cancelRequestByName(final String name) {
        if (TextUtils.isEmpty(name)) {
            return;
        }

        // Traverse all async http calls, if the name is the same as the given parameter, cancel it
        for (ExecutableCall call: allAsyncHttpCall) {
            if (call != null && name.equals(call.getRequestName())) {
                call.cancel();
            }
        }
    }

    @Override
    public void cancelAllRequest() {
        // Traverse all async http calls, if the name is the same as the given parameter, cancel it
        for (ExecutableCall call: allAsyncHttpCall) {
            call.cancel();
        }
    }

    /**
     * Get all Interceptors
     */
    @Override
    public ArrayList<Interceptor> getAllInterceptors() {
        final ArrayList<Interceptor> all = new ArrayList<>();
        all.addAll(frontInterceptors);
        all.addAll(builtinInterceptors);
        all.addAll(endInterceptors);

        return all;
    }

    /**
     * Get all built-in
     */
    @Override
    public ArrayList<Interceptor> getBuiltinInterceptors() {
        return new ArrayList<>(builtinInterceptors);
    }

    /**
     * Add a custom Interceptor to the head of the Interceptor queue
     */
    @Override
    public synchronized void addFistInterceptor(final Interceptor it) {
        if (it != null) {
            frontInterceptors.add(it);
        }
    }

    @Override
    public void setDefaultMaxRetryTimes(final int maxRetryTimes) {
        this.defaultMaxRetryTimes = maxRetryTimes;
    }

    @Override
    public int getDefaultMaxRetryTimes() {
        return defaultMaxRetryTimes;
    }

    /**
     * Add Interceptor at the end of the Interceptor queue
     */
    @Override
    public synchronized void addLastInterceptor(final Interceptor it) {
        if (it != null) {
            endInterceptors.add(it);
        }
    }

    @Override
    public Dispatcher dispatcher() {
        return mDispatcher;
    }

    @Override
    public Delivery delivery() {
        return delivery;
    }


    @SuppressWarnings("unused")
    @Override
    public void setDelivery(final Delivery delivery) {
        if (delivery != null) {
            this.delivery = delivery;
        }
    }

    /**
     * Interface used to wrap Request into HttpCall
     *
     * @param request needs to be packaged into HttpCall Request
     * @return wrapped HttpCall object
     */
    @Override
    public Call newCall(Request request) {
        return new HttpCall(this, request);
    }
}
