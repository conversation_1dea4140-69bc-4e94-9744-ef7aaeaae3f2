package com.centauri.http.core;

import android.text.TextUtils;

import java.net.HttpURLConnection;

/**
 * The result of the network request
 *
 * Created by a1w0n on 9/8/16.
 */
public class Response {

    // The requested result code, default -1
    public int resultCode = -1;

    // The binary data returned by the network request is converted to the content of String
    public String responseBody;

    // If there is an Exception during the request, use this variable to record
    // In the case of resultCode != 200, this variable is meaningful
    // Default is empty
    public Exception exception = null;

    private Request request;

    // Do you want to terminate the processing of the remaining Interceptor in the entire Interceptor chain
    // If true, the subsequent Interceptor in the Interceptor chain will be ignored, and the response will be
    // As the final processing result of the entire Interceptor chain
    public boolean needBreakOtherInterceptors = false;

    // Has the Request corresponding to this Response been stopped?
    public boolean isStopped = false;

    public final void setRequest(final Request request) {
        this.request = request;
    }

    // A tag object, which can store anything to facilitate external customization
    private Object tag;

    public final Request request() {
        return request;
    }

    public final boolean isHttpsResponse() {
        return request != null && request.isHttpsRequest();
    }

    public final void setTag(Object object) {
        if (object != null) {
            tag = object;
        }
    }

    public final Object getTag() {
        return tag;
    }

    /**
     * Return whether this request is a successful Http request
     */
    public final boolean isSuccess() {
        return isResultCodeOK() && !TextUtils.isEmpty(responseBody) && exception == null;
    }

    public final boolean isResultCodeOK() {
        return resultCode == HttpURLConnection.HTTP_OK;
    }

    /**
     * Whether the Http request returned a non-empty body
     */
    public final boolean hasNotEmptyBody() {
        return !TextUtils.isEmpty(responseBody);
    }

    /**
     * Is there any abnormality in this connection?
     */
    public final boolean hasException() {
        return exception != null;
    }

    /**
     * Reset the mark bit of needBreakOtherInterceptors to the default value
     * Prevent him from affecting places that shouldn't be affected
     */
    public final void resetNeedBreakOtherInterceptors() {
        needBreakOtherInterceptors = false;
    }
}
