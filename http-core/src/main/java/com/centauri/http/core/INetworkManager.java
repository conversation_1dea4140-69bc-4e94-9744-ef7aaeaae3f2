package com.centauri.http.core;

import java.util.ArrayList;

/**
 * Created by cheneyang on 2017/10/31.
 */

public interface INetworkManager extends Call.Factory {

    void addHttpHandler(HttpHandler httpHandler);

    void registerAsyncHttpCall(final ExecutableCall call);

    void unregisterAsyncHttpCall(final ExecutableCall call);

    void cancelRequestByName(final String name);

    void cancelAllRequest();

    ArrayList<Interceptor> getAllInterceptors();

    ArrayList<Interceptor> getBuiltinInterceptors();

    void addFistInterceptor(final Interceptor it);

    void setDefaultMaxRetryTimes(int maxRetryTimes);

    int getDefaultMaxRetryTimes();

    void addLastInterceptor(final Interceptor it);

    Dispatcher dispatcher();

    Delivery delivery();

    void setDelivery(final Delivery delivery);
}
