package com.circinus.http.core;

import android.os.Handler;
import android.os.Looper;

import java.util.concurrent.Executor;

//import com.circinus.circinus.comm.//HttpLog;

/**
 * Distributor that distributes the results to the main thread
 *
 * Created by a1w0n on 9/8/16.
 */
public class MainThreadDelivery implements Delivery {

    private MainThreadExecutor executor;

    public MainThreadDelivery() {
        executor = new MainThreadExecutor();
    }

    @Override
    public void deliverResult(final Response response, final Callback callback) {
        if (callback == null) {
            return;
        }

        executor.execute(new Runnable() {
            @Override
            public void run() {
                callback.onResponse(response);
            }
        });
    }

    private static class MainThreadExecutor implements Executor {

        private final Handler handler = new Handler(Looper.getMainLooper());

        @Override
        public void execute(Runnable r) {
            handler.post(r);
        }
    }
}
