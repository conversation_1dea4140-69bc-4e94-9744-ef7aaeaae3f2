package com.circinus.http.core;

import android.os.Process;

import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.ThreadFactory;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * Asynchronous distribution executor for network requests
 *
 * Created by a1w0n on 9/8/16.
 */
public class Dispatcher {

    // The maximum number of concurrent threads in the thread pool
    private static final int MAX_CURRENCY_NETWORK_THREAD = 5;

    // Thread Pool
    private ExecutorService executorService;

    public Dispatcher() {
        // Create a new thread pool
        executorService = getExecutorService();
    }

    /**
     * Insert a concurrent task, the task package is AsyncHttpCall
     *
     * @param call The package object of the task
     */
    public synchronized void enqueue(final ExecutableCall call) {
        if (call == null) {
            return;
        }

        // Let the thread pool perform tasks
        executorService.execute(call);
    }

    /**
     * Construct a thread pool, if it already exists, return directly to the existing one
     */
    private synchronized ExecutorService getExecutorService() {
        if (executorService == null) {
            // Prevent the number of threads from skyrocketing
            executorService = Executors.newFixedThreadPool(
                    MAX_CURRENCY_NETWORK_THREAD,
                    threadFactory("Network Thread", false));
        }

        return executorService;
    }

    /**
     * Get a thread pool factory, used to produce threads
     *
     * @param name the name of the thread
     * @param daemon whether to guard the thread
     * @return returns a thread pool factory
     */
    private static ThreadFactory threadFactory(final String name, final boolean daemon) {

        return new ThreadFactory() {

            // Take the counter of the threads produced
            private AtomicInteger netThreadCount = new AtomicInteger(0);

            @Override
            public Thread newThread(Runnable runnable) {
                final Thread result = new Thread(
                        runnable,
                        name + "" + netThreadCount.getAndIncrement());
                // Set thread priority to prevent CPU resources from competing with the main thread
                result.setPriority(Process.THREAD_PRIORITY_BACKGROUND);
// Process.setThreadPriority(Process.THREAD_PRIORITY_BACKGROUND);
                // Whether it is a daemon according to the parameter setting
                result.setDaemon(daemon);
                return result;
            }
        };
    }
}
