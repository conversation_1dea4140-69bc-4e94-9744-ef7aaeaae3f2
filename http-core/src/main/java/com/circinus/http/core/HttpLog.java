package com.circinus.http.core;

/**
 * Log function
 * All printing of the network module will be called here
 *
 * Created by a1w0n on 2018/1/3.
 */
public class HttpLog {

    static IHttpLog httpLogInterface;

    public static void e(final String tag, final String msg) {
        if (httpLogInterface != null) {
            httpLogInterface.e(tag, msg);
        }
    }

    public static void d(final String tag, final String msg) {
        if (httpLogInterface != null) {
            httpLogInterface.d(tag, msg);
        }
    }

    public static void i(final String tag, final String msg) {
        if (httpLogInterface != null) {
            httpLogInterface.i(tag, msg);
        }
    }

    public static void w(final String tag, final String msg) {
        if (httpLogInterface != null) {
            httpLogInterface.w(tag, msg);
        }
    }
}
