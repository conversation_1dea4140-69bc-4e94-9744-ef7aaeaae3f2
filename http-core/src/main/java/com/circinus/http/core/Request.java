package com.circinus.http.core;

import android.os.Looper;
import android.text.TextUtils;

import com.circinus.comm.CTILog;

import org.json.JSONException;
import org.json.JSONObject;

import java.net.HttpURLConnection;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.atomic.AtomicBoolean;

import javax.net.ssl.HostnameVerifier;
import javax.net.ssl.SSLSocketFactory;

/**
 * A network request package
 */
public class Request {

    private static final String TAG = "Request";
    // The start time of this request, this time will not be updated even if it fails and retries later
    public long startTime = 0;

    // The start time of this request
    // startTime is the time when the entire request starts, after each failed retry, startTime will not change
    // And currentTryTime will be updated every time a request
    // If it is the first time to try to connect to the Internet,
    // currentTryTime will also be set. At this time, currentTryTime and startTime are the same
    public long currentTryTime = 0;

    // The time consumed by this request
    // Before the start of each network request, a timestamp A will be recorded,
    // and when the network request ends, a timestamp B will be recorded
    // currentTryTimeConsume = B-A
    public long currentTryTimeConsume = 0;

    // This network request, the time consumed by getOutPutStream
    public long currentGetOutputStreamTime = -1;
    // This network request, the time consumed by getInputStream
    public long currentGetInputStreamTime = -1;

    // How many retries have passed, the default is 0 times, and after each failure, 1 will be added here
    public int retryTimes = 0;

    // Whether all retries have failed
    public boolean isAllRetriesFailed = false;

    // The default value here is -1, it won't work
    // Only when the subclass sets a value of >=0 will it be effective
    @SuppressWarnings("WeakerAccess")
    public int maxRetryTime = -1;

    // Used to mark whether the current request has been stopped
    // If the request is stopped, the external request result should not be called back
    private AtomicBoolean stopped = new AtomicBoolean(false);

    // URL used in this network request
    private HttpURL httpUrl;

    // The default is POST method
    // Prevent the URL from being too long if the parameter is too long, and then the server returns 414
    public String method = Method.POST;

    // The main thread dispatcher is used by default
    @SuppressWarnings("WeakerAccess")
    public Delivery delivery;

    // Http request headers that need to be passed to the server
    private HashMap<String, String> headers = new HashMap<>();
    // Http request parameters that need to be passed to the server,
    // if it is a POST request parameter, it needs to be written to the stream
    // If it is a GET request parameter, it needs to be written behind the URL
    private HashMap<String, String> parameters = new HashMap<>();
    private HashMap<String, Object> keyParameters = new HashMap<>();

    // Subclasses can customize the timeout time here
    // It will work only if it is not 0
    public int connectTimeout = 0;
    @SuppressWarnings("WeakerAccess")
    public int readTimeout = 0;

    // These two allow subclasses to customize some certificate verification related settings of Https
    // Only works when the current connection is Https connection
    private HostnameVerifier hostnameVerifier;
    private SSLSocketFactory sslSocketFactory;

    HttpURLConnection httpURLConnection;

    /**
     * Before requesting the network, reset some fields to prevent special circumstances from affecting the next request
     * For example, the first request to set currentGetOutputStreamTime = 300, but the request failed
     * The next request failed abnormally before the value of currentGetOutputStreamTime was updated,
     * resulting in the second request to report
     * currentGetOutputStreamTime is 300, in fact, this 300 is the last time
     */
    public void resetGetOutputStreamTimeAndGetInputStreamTime() {
        currentGetOutputStreamTime = -1;
        currentGetInputStreamTime = -1;
    }


    /**
     * Set Https verification related objects
     *
     * @param verifier HostnameVerifier
     */
    public final void setCustomHostnameVerifier(final HostnameVerifier verifier) {
        if (verifier != null) {
            this.hostnameVerifier = verifier;
        }
    }

    public final void clearCustomHostnameVerifier() {
        hostnameVerifier = null;
    }

    public final void clearCustomSSLSocketFactory() {
        sslSocketFactory = null;
    }

    /**
     * Set Https verification related objects
     *
     * @param factory SSLSocketFactory
     */
    public void setCustomSSLSocketFactory(final SSLSocketFactory factory) {
        if (factory != null) {
            this.sslSocketFactory = factory;
        }
    }

    @SuppressWarnings("WeakerAccess")
    public HostnameVerifier getCustomHostnameVerifier() {
        return hostnameVerifier;
    }


    @SuppressWarnings("WeakerAccess")
    public SSLSocketFactory getCustomSSLSocketFactory() {
        return sslSocketFactory;
    }

    /**
     * Set the domain name corresponding to the request
     */
    public void setURL(final HttpURL httpURL) {
        if (httpURL == null) {
            return;
        }

        this.httpUrl = httpURL;
    }

    public final void addHttpHeader(final String key, final String value) {
        // value can be a null character, but not a null pointer
        if (!TextUtils.isEmpty(key) && value != null) {
            headers.put(key, value);
        }
    }

    public final boolean hasHttpHeader(final String key, final String value) {
        if (TextUtils.isEmpty(key)) {
            return false;
        }

        // value can be a null character, but not a null pointer
        if (value == null) {
            return false;
        }

        final String targetValue = headers.get(key);

        // targetValue is null, indicating that no such header exists
        return targetValue != null && value.equals(targetValue);
    }

    /**
     * Remove an Http Header, and remove it only when the key and value are the same
     *
     * @param key   The key of the Header that needs to be removed
     * @param value The value of the Header that needs to be removed
     */
    public final void removeHttpHeader(final String key, final String value) {
        if (TextUtils.isEmpty(key)) {
            return;
        }

        // value can be a null character, but not a null pointer
        if (value == null) {
            return;
        }

        final String targetValue = headers.get(key);
        // is null, indicating that no such header exists
        if (targetValue == null) {
            return;
        }

        if (value.equals(targetValue)) {
            headers.remove(key);
        }
    }

    @SuppressWarnings("WeakerAccess")
    public final void addHttpParameters(final String key, final String value) {
        if (!TextUtils.isEmpty(key)) {
            parameters.put(key, value);
        }
    }

    @SuppressWarnings("WeakerAccess")
    public final void addHttpKeyParameters(final String key, final Object value) {
        if (!TextUtils.isEmpty(key)) {
            keyParameters.put(key, value);
        }
    }
    /**
     * Piece together all request parameters
     */
    public String constructAllParams() {
        if (this.parameters.size() <= 0) {
            return "";
        } else {
            boolean isNewOfficialFound = false;

            for (Map.Entry<String, String> entry : this.parameters.entrySet()) {
                String key = entry.getKey();
                String value = entry.getValue();

                if (key.equals("isNewOfficial") && value.equals("1")) {
                    isNewOfficialFound = true;
                    break;
                }
            }

            if (isNewOfficialFound) {
                JSONObject jsonParams = new JSONObject();

                for (Map.Entry<String, String> entry : this.parameters.entrySet()) {
                    String key = entry.getKey();
                    String value = entry.getValue();

                    if (!key.equals("isNewOfficial")) {
                        try {
                            jsonParams.put(key, value);
                        } catch (JSONException e) {
                            e.printStackTrace();
                        }
                    }
                }
                for (Map.Entry<String, Object> entry : this.keyParameters.entrySet()) {
                    String key = entry.getKey();
                    Object value = entry.getValue();
                    try {
                        jsonParams.put(key, value);
                    } catch (JSONException e) {
                        e.printStackTrace();
                    }
                }
                String allParamsStr = jsonParams.toString();
                CTILog.d("Request json ", "constructAllParams = " + allParamsStr);
                return allParamsStr;
            } else {
                StringBuilder params = new StringBuilder("");

                for (Map.Entry<String, String> entry : this.parameters.entrySet()) {
                    String key = entry.getKey();
                    String value = entry.getValue();

                    params.append(key);
                    params.append("=");
                    params.append(value);
                    params.append("&");
                }

                if (params.length() > 0) {
                    params.deleteCharAt(params.length() - 1);
                }

                String allParamsStr = params.toString();
                CTILog.d("Request kv ", "constructAllParams = " + allParamsStr);
                return allParamsStr;
            }
        }
    }
//    public String constructAllParams() {
//        if (parameters.size() <= 0) {
//            return "";
//        }
//
//        final StringBuilder params = new StringBuilder("");
//
//        for (Map.Entry<String, String> entry : parameters.entrySet()) {
//            params.append(entry.getKey());
//            params.append("=");
//            params.append(entry.getValue());
//            params.append("&");
//        }
//
//        // delete the last ampersand
//        if (params.length() > 0) {
//            params.deleteCharAt(params.length() - 1);
//        }
//        String allParamsStr = params.toString();
//        CTILog.d(TAG, "constructAllParams = " + allParamsStr);
//        return allParamsStr;
//    }

    public HashMap<String, String> getHttpHeaders() {
        return headers;
    }

    @SuppressWarnings("WeakerAccess")
    public HashMap<String, String> getParameters() {
        return parameters;
    }

    public String getParameter(final String key) {
        if (TextUtils.isEmpty(key)) {
            return "";
        }

        if (!parameters.containsKey(key)) {
            return "";
        }

        return parameters.get(key);
    }

    public HttpURL getHttpUrl() {
        return httpUrl;
    }

    /**
     * Return whether this request is an Https request
     */
    public boolean isHttpsRequest() {
        return httpUrl != null && httpUrl.isSchemaHttps();
    }

    /**
     * Return whether this request is a POST request
     */
    boolean isPostRequest() {
        return Method.POST.equals(method);
    }

    /**
     * Returns whether the host of the current request is an ip address
     */
    public boolean isRequestWithIP() {
        return httpUrl != null && httpUrl.hostIsIP();
    }

    /**
     * Return whether this request is a GET request
     */
    boolean isGetRequest() {
        return Method.GET.equals(method);
    }

    public boolean hasParameters() {
        return parameters.size() > 0;
    }

    /**
     * If it is a GET request, the request parameters need to be pieced together to the end of the URL
     * If it is a POST request, the request parameters do not need to be pieced together to the end of the URL,
     * and then written in a stream
     */
    public String getFullURLString() {
        if (httpUrl == null) {
            return "";
        }

        final String fullURL = httpUrl.getFullUrlString();

        if (isGetRequest() && hasParameters()) {
            return fullURL + "?" + constructAllParams();
        }

        return fullURL;
    }

    /**
     * Set the host of this request
     */
    public void setHost(final String host) {
        if (httpUrl != null) {
            httpUrl.host = host;
        }
    }

    public void setPort(final String port) {
        if (httpUrl != null) {
            httpUrl.port = port;
        }
    }

    /**
     * Return the host of this request
     */
    public String getHost() {
        if (httpUrl != null) {
            return httpUrl.host;
        }

        return "";
    }

    public String getUrlSuffix() {
        if (httpUrl != null) {
            return httpUrl.suffix;
        }

        return "";
    }

    /**
     * When this Request is retried during network processing, this method will be called back
     *
     * @param currentRetry The current number of retries
     * @param maxRetry     maximum number of retries
     * @param request      Request
     * @param response     Response generated by the last request
     */
    @SuppressWarnings("WeakerAccess")
    public void onHttpRetry(int currentRetry, int maxRetry, Request request, Response response) {
    }

    /**
     * When this Request is about to start a network request during network processing
     * Will call back this method
     */
    @SuppressWarnings("WeakerAccess")
    public void onHttpStart() {
    }

    /**
     * When this Request is in the process of network processing when the network request ends
     * Will call back this method
     */
    @SuppressWarnings("WeakerAccess")
    public void onHttpEnd(Response response) {
    }

    /**
     * Stop this request, if the request is stopped, the outside will not get any result callback
     */
    public void stopRequest() {
        stopped.set(true);

        // Cannot disconnect the network on the main thread
        if (Looper.myLooper() == Looper.getMainLooper()) {
            new Thread(new Runnable() {
                @Override
                public void run() {
                    if (httpURLConnection != null) {
                        httpURLConnection.disconnect();
                    }
                }
            }).start();
        } else {
            if (httpURLConnection != null) {
                httpURLConnection.disconnect();
            }
        }
    }

    /**
     * Return whether this request has been stopped
     */
    public boolean isStopped() {
        return stopped.get();
    }

    public interface Method {
        String POST = "POST";
        String GET = "GET";
    }

}
