package com.circinus.http.core;

/**
 * Unlike Interceptor, <PERSON>ttpHandler mainly cooperates with HttpInterceptor
 * At different stages of Http request, there will be corresponding life cycle function callbacks to <PERSON>ttp<PERSON>and<PERSON>,
 * and it is called back by HttpInterceptor (
 * Because only he knows the current status of the Http request)
 *
 * A typical application scenario: set a custom Https certificate validator before Https request,
 * and restore to the default Https certificate validator after Https request
 *
 * Created by a1w0n on 2017/2/7.
 */
public interface HttpHand<PERSON> {

    void onHttpStart(final Request request);

    void onHttpEnd(final Request request, final Response response);

    /**
     * This function will be called back when the last network request failed, and it will be retried all the time
     *
     * @param currentRetry is about to retry the first few times
     * @param maxRetry maximum number of retries
     * @param request The request that needs to be retried
     * @param response The last failed response
     */
    void onHttpRetry(final int currentRetry, final int maxRetry,
                     final Request request, final Response response);
}
