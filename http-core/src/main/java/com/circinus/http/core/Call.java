package com.circinus.http.core;

/**
 * The package of a task, the task can be executed, canceled, etc.
 *
 * Created by a1w0n on 9/8/16.
 */
public interface Call {

    void cancel();

    // Run asynchronously, so the result callback is needed
    void enqueue(Callback callback);

    void enqueueWithNoCustomInterceptor(Callback callback);

    boolean isExecuted();

    boolean isCanceled();

    // Direct synchronous operation
    Response execute();

    Response executeWithNoCustomInterceptor();

    Response executeWithAllCustomInterceptor();

    interface Factory {
        Call newCall(Request request);
    }
}
