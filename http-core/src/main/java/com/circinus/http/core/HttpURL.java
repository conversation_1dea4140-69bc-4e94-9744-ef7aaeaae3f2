package com.circinus.http.core;

import android.text.TextUtils;

import com.circinus.http.utils.CTIIPValidator;


/**
 * Encapsulation of URL corresponding to network request
 *
 * Created by a1w0n on 9/8/16.
 */
public final class HttpURL {

    // used host
    public String host;
    // Port used
    public String port;
    // url suffix, go out the rest of the schema, host, port
    public String suffix;
    // Schema used
    public String schema;

    public HttpURL(final String schema, final String host) {
        this.schema = schema;
        this.host = host;
    }

    String getFullUrlString() {
        final StringBuilder builder = new StringBuilder();

        if (SCHEMA.HTTP.equals(schema)) {
            builder.append("http://");
        } else if (SCHEMA.HTTPS.equals(schema)) {
            builder.append("https://");
        }

        if (!TextUtils.isEmpty(host)) {
            if(CTIIPValidator.validateIPV6(host)){
                builder.append("[" + host + "]");
            }else{
                builder.append(host);
            }
        }

        if (!TextUtils.isEmpty(port)) {
            builder.append(":");
            builder.append(port);
        }

        if (!TextUtils.isEmpty(suffix)) {
            // If suffix starts with "/", there will be an extra "/", we check it to prevent more
            if (suffix.startsWith("/")) {
                suffix = suffix.substring(1, suffix.length());
            }
            builder.append("/");
            builder.append(suffix);
        }

        return builder.toString();
    }

    /**
     * Is the host of this URL an ip address?
     * If host is empty, false is returned by default
     */
    boolean hostIsIP() {
        return !TextUtils.isEmpty(host) && CTIIPValidator.isIPAddress(host);
    }

    /**
     * Is it an Https request
     */
    boolean isSchemaHttps() {
        return SCHEMA.HTTPS.equals(schema);
    }

    /**
     * Two schemas of Http request
     */
    public interface SCHEMA {
        String HTTP = "http";
        String HTTPS = "https";
    }

}
