package com.circinus.http.circinushttp;

import com.circinus.http.core.Request;

/**
 * Since the timeout period of the mainline version can be issued dynamically,
 * the timeout period must be reset once before each request
 * Responsible for setting the timeout period of this request before each request
 *
 * Created by a1w0n on 2017/2/7.
 */
public final class CTITimeoutHandler extends CTIBaseHttpHandler {

    private CTINetworkManager newNetworkManager;

    CTITimeoutHandler(final CTINetworkManager networkManager) {
        newNetworkManager = networkManager;
    }

    @Override
    public final void onHttpStart(final Request request) {
        super.onHttpStart(request);

        if (request == null) {
            return;
        }

        if (newNetworkManager == null) {
            return;
        }

        newNetworkManager.updateConnectAndReadTimeout();
    }
}
