package com.circinus.http.circinushttp;

import com.circinus.http.core.Request;
import com.circinus.http.core.Response;

/**
 * Interface used for data reporting
 *
 * Created by a1w0n on 2017/3/8.
 */
public interface ICTIDataReportNotifier {

    /**
     * Each time a network request is successful, this method will be called back
     * As long as the Http request is successful, it will be called back,
     * regardless of whether the Mi Master’s business is successful or not
     *
     * @param request corresponding request
     * @param response corresponding response
     */
    void onNetworkSuccess(final Request request, final Response response);

    /**
     * Every network request, if it fails, this method will be called back
     * As long as the Http request fails
     *
     * @param request corresponding request
     * @param response corresponding response
     */
    void onNetworkFailure(final Request request, final Response response);
}

