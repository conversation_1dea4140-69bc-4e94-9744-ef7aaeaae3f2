apply plugin: 'maven'

//task androidJavadocs(type: Javadoc) {
//    source = android.sourceSets.main.java.srcDirs
//    classpath += project.files(android.getBootClasspath().join(File.pathSeparator))
//}

//task androidJavadocsJar(type: Jar, dependsOn: androidJavadocs) {
//    classifier = 'javadoc'
//    from androidJavadocs.destinationDir
//}

task androidSourcesJar(type: Jar) {
    classifier = 'sources'
    from android.sourceSets.main.java.srcDirs
}

artifacts {
    archives androidSourcesJar
//    archives androidJavadocsJar
}

uploadArchives {
    repositories {
        mavenDeployer {
            // 这里仓库地址可改成自己需要的
            repository(url: "https://mirrors.tencent.com/repository/maven/thirdparty/") {
                // 这里的用户名和密码需要写在local.properties
                // 删除代码中上传maven仓库的用户名与代码
            }

            pom.groupId = 'com.circinus.comm'
            pom.artifactId = 'circinuscommon-http'
            pom.version = '2.00.01'

            pom.project {
                licenses {
                    license {
                        name 'The Apache Software License, Version 2.0'
                        url 'http://www.apache.org/licenses/LICENSE-2.0.txt'
                    }
                }
            }
        }
    }
}



// 上传到内部私服maven仓库（nexus搭建）命令：gradle uploadArchives