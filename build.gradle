// Top-level build file where you can add configuration options common to all sub-projects/modules.

buildscript {
    repositories {


        maven {
            //tencent_public
            url 'https://mirrors.tencent.com/nexus/repository/maven-public/'
        }
        maven {
            //thirdparty
            url 'https://mirrors.tencent.com/repository/maven/thirdparty-snapshots/'
        }
        maven {
            //thirdparty-snapshots
            url 'https://mirrors.tencent.com/repository/maven/thirdparty/'
        }


    }
    dependencies {

        classpath 'com.android.tools.build:gradle:4.1.0'
        // NOTE: Do not place your application dependencies here; they belong
        // in the individual module build.gradle files
    }
}

allprojects {
    repositories {
        flatDir {
            dirs 'libs'
        }

        maven {
            //tencent_public
            url 'https://mirrors.tencent.com/nexus/repository/maven-public/'
        }z
        maven {
            //thirdparty
            url 'https://mirrors.tencent.com/repository/maven/thirdparty-snapshots/'
        }
        maven {
            //thirdparty-snapshots
            url 'https://mirrors.tencent.com/repository/maven/thirdparty/'
        }
    }
}

// rootProject build.gradle中定义全局方法
/**
 * 获取当前的git的commit_id
 * @return
 */
def getGitRevision() {
    return "git rev-parse --short HEAD".execute().text.trim()
}

/**
 * 获取git当前分支,去除"branch/"与"-"
 * @return
 */
def getGitBranch() {
    return 'git symbolic-ref --short -q HEAD'.execute().text.trim().replace("branch/","").replace("_","")
}

/**
 * 获取 build package 时间
 * @return
 */
static def getReleaseTime() {
    return new Date().format("yyyyMMdd", TimeZone.getDefault());
}

task clean(type: Delete) {
    delete rootProject.buildDir
}
